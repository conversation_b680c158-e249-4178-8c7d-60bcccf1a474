import Link from "next/link";
import {
  ShoppingCart,
  Bot,
  Search,
  Zap,
  Shield,
  Globe,
  CheckCircle,
  ArrowRight
} from "lucide-react";
import Testimonials from "@/components/Testimonials";
import Header from "@/components/Header";
import Footer from "@/components/Footer";

export default function Home() {
  return (
    <>
      <Header />
      {/* Hero Section */}
      <section
        id="home"
        className="relative z-10 overflow-hidden bg-white dark:bg-gray-dark hero-pattern pb-16 pt-[120px] md:pb-[120px] md:pt-[150px] xl:pb-[160px] xl:pt-[180px] 2xl:pb-[200px] 2xl:pt-[210px]"
      >
        <div className="container">
          <div className="-mx-4 flex flex-wrap">
            <div className="w-full px-4">
              <div className="mx-auto max-w-[800px] text-center">
                <h1 className="mb-5 text-3xl font-bold leading-tight text-black dark:text-white sm:text-4xl sm:leading-tight md:text-5xl md:leading-tight">
                  Automate Your{" "}
                  <span className="text-primary">
                    TikTok Shop
                  </span>{" "}
                  Success
                </h1>
                <p className="mb-12 text-base !leading-relaxed text-body-color dark:text-muted-foreground sm:text-lg md:text-xl">
                  Complete TikTok Shop management platform with automated product crawling,
                  intelligent listing, and seamless order fulfillment. Scale your business
                  with AI-powered automation.
                </p>
                <div className="flex flex-col items-center justify-center space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0">
                  <Link
                    href="/login"
                    className="rounded-sm bg-primary px-8 py-4 text-base font-semibold text-white duration-300 ease-in-out hover:bg-primary/80"
                  >
                    🚀 Get Started Free
                  </Link>
                  <Link
                    href="#features"
                    className="inline-block rounded-sm bg-black px-8 py-4 text-base font-semibold text-white duration-300 ease-in-out hover:bg-black/90 dark:bg-white/10 dark:text-white dark:hover:bg-white/5"
                  >
                    Learn More
                  </Link>
                </div>
                <div className="mt-8 flex items-center justify-center space-x-6 text-sm text-body-color dark:text-muted-foreground">
                  <div className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-primary" />
                    3-day free trial
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-primary" />
                    No setup fees
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-primary" />
                    Cancel anytime
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="absolute right-0 top-0 z-[-1] opacity-30 lg:opacity-100">
          <svg
            width="450"
            height="556"
            viewBox="0 0 450 556"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle
              cx="277"
              cy="63"
              r="225"
              fill="url(#paint0_linear_25:217)"
            />
            <circle
              cx="17.9997"
              cy="182"
              r="18"
              fill="url(#paint1_radial_25:217)"
            />
            <circle
              cx="76.9997"
              cy="288"
              r="34"
              fill="url(#paint2_radial_25:217)"
            />
            <circle
              cx="325.486"
              cy="302.87"
              r="180"
              transform="rotate(-37.6852 325.486 302.87)"
              fill="url(#paint3_linear_25:217)"
            />
            <circle
              opacity="0.8"
              cx="184.521"
              cy="315.521"
              r="132.862"
              transform="rotate(114.874 184.521 315.521)"
              stroke="url(#paint4_linear_25:217)"
            />
            <circle
              opacity="0.8"
              cx="356"
              cy="290"
              r="179.5"
              transform="rotate(-30 356 290)"
              stroke="url(#paint5_linear_25:217)"
            />
            <circle
              opacity="0.8"
              cx="191.659"
              cy="302.659"
              r="133.362"
              transform="rotate(133.319 191.659 302.659)"
              fill="url(#paint6_linear_25:217)"
            />
            <defs>
              <linearGradient
                id="paint0_linear_25:217"
                x1="-54.5003"
                y1="-178"
                x2="222"
                y2="288"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#4A6CF7" />
                <stop offset="1" stopColor="#4A6CF7" stopOpacity="0" />
              </linearGradient>
              <radialGradient
                id="paint1_radial_25:217"
                cx="0"
                cy="0"
                r="1"
                gradientUnits="userSpaceOnUse"
                gradientTransform="translate(17.9997 182) rotate(90) scale(18)"
              >
                <stop offset="0.145833" stopColor="#4A6CF7" stopOpacity="0" />
                <stop offset="1" stopColor="#4A6CF7" stopOpacity="0.08" />
              </radialGradient>
              <radialGradient
                id="paint2_radial_25:217"
                cx="0"
                cy="0"
                r="1"
                gradientUnits="userSpaceOnUse"
                gradientTransform="translate(76.9997 288) rotate(90) scale(34)"
              >
                <stop offset="0.145833" stopColor="#4A6CF7" stopOpacity="0" />
                <stop offset="1" stopColor="#4A6CF7" stopOpacity="0.08" />
              </radialGradient>
              <linearGradient
                id="paint3_linear_25:217"
                x1="226.775"
                y1="-66.1548"
                x2="292.157"
                y2="351.421"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#4A6CF7" />
                <stop offset="1" stopColor="#4A6CF7" stopOpacity="0" />
              </linearGradient>
              <linearGradient
                id="paint4_linear_25:217"
                x1="184.521"
                y1="182.159"
                x2="184.521"
                y2="448.882"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#4A6CF7" />
                <stop offset="1" stopColor="white" stopOpacity="0" />
              </linearGradient>
              <linearGradient
                id="paint5_linear_25:217"
                x1="356"
                y1="110"
                x2="356"
                y2="470"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#4A6CF7" />
                <stop offset="1" stopColor="white" stopOpacity="0" />
              </linearGradient>
              <linearGradient
                id="paint6_linear_25:217"
                x1="118.524"
                y1="29.2497"
                x2="166.965"
                y2="338.63"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#4A6CF7" />
                <stop offset="1" stopColor="#4A6CF7" stopOpacity="0" />
              </linearGradient>
            </defs>
          </svg>
        </div>
        <div className="absolute bottom-0 left-0 z-[-1] opacity-30 lg:opacity-100">
          <svg
            width="364"
            height="201"
            viewBox="0 0 364 201"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M5.88928 72.3303C33.6599 66.4798 101.397 64.9086 150.178 105.427C211.155 156.076 229.59 162.093 264.333 166.607C299.076 171.12 337.718 183.657 362.889 212.24"
              stroke="url(#paint0_linear_25:218)"
            />
            <path
              d="M-22.1107 72.3303C5.65989 66.4798 73.3965 64.9086 122.178 105.427C183.155 156.076 201.59 162.093 236.333 166.607C271.076 171.12 309.718 183.657 334.889 212.24"
              stroke="url(#paint1_linear_25:218)"
            />
            <path
              d="M-53.1107 72.3303C-25.3401 66.4798 42.3965 64.9086 91.1783 105.427C152.155 156.076 170.59 162.093 205.333 166.607C240.076 171.12 278.718 183.657 303.889 212.24"
              stroke="url(#paint2_linear_25:218)"
            />
            <path
              d="M-98.1618 65.0889C-68.1416 60.0601 4.73364 60.4882 56.0734 102.431C120.248 154.86 139.905 161.419 177.137 166.956C214.37 172.493 255.575 186.165 281.856 215.481"
              stroke="url(#paint3_linear_25:218)"
            />
            <circle
              opacity="0.8"
              cx="214.505"
              cy="60.5054"
              r="49.7205"
              transform="rotate(-13.421 214.505 60.5054)"
              stroke="url(#paint4_linear_25:218)"
            />
            <circle cx="220" cy="63" r="43" fill="url(#paint5_radial_25:218)" />
            <defs>
              <linearGradient
                id="paint0_linear_25:218"
                x1="184.389"
                y1="69.2405"
                x2="184.389"
                y2="212.24"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#4A6CF7" stopOpacity="0" />
                <stop offset="1" stopColor="#4A6CF7" />
              </linearGradient>
              <linearGradient
                id="paint1_linear_25:218"
                x1="156.389"
                y1="69.2405"
                x2="156.389"
                y2="212.24"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#4A6CF7" stopOpacity="0" />
                <stop offset="1" stopColor="#4A6CF7" />
              </linearGradient>
              <linearGradient
                id="paint2_linear_25:218"
                x1="125.389"
                y1="69.2405"
                x2="125.389"
                y2="212.24"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#4A6CF7" stopOpacity="0" />
                <stop offset="1" stopColor="#4A6CF7" />
              </linearGradient>
              <linearGradient
                id="paint3_linear_25:218"
                x1="93.8507"
                y1="67.2674"
                x2="89.9278"
                y2="210.214"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#4A6CF7" stopOpacity="0" />
                <stop offset="1" stopColor="#4A6CF7" />
              </linearGradient>
              <linearGradient
                id="paint4_linear_25:218"
                x1="214.505"
                y1="10.2849"
                x2="212.684"
                y2="99.5816"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#4A6CF7" />
                <stop offset="1" stopColor="#4A6CF7" stopOpacity="0" />
              </linearGradient>
              <radialGradient
                id="paint5_radial_25:218"
                cx="0"
                cy="0"
                r="1"
                gradientUnits="userSpaceOnUse"
                gradientTransform="translate(220 63) rotate(90) scale(43)"
              >
                <stop offset="0.145833" stopColor="white" stopOpacity="0" />
                <stop offset="1" stopColor="white" stopOpacity="0.08" />
              </radialGradient>
            </defs>
          </svg>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-16 md:py-20 lg:py-28">
        <div className="container">
          <div className="mx-auto max-w-[570px] text-center mb-[100px]">
            <h2 className="mb-4 text-3xl font-bold !leading-tight text-black dark:text-white sm:text-4xl md:text-[45px]">
              Everything You Need to Scale
            </h2>
            <p className="text-base !leading-relaxed text-body-color dark:text-muted-foreground md:text-lg">
              Powerful features designed to automate and optimize your TikTok Shop operations
            </p>
          </div>

          <div className="grid grid-cols-1 gap-x-8 gap-y-14 md:grid-cols-2 lg:grid-cols-3">
            <div className="w-full">
              <div>
                <div className="mb-10 flex h-[70px] w-[70px] items-center justify-center rounded-md bg-primary/10 text-primary">
                  <Search className="h-8 w-8" />
                </div>
                <h3 className="mb-5 text-xl font-bold text-black dark:text-white sm:text-2xl lg:text-xl xl:text-2xl">
                  Smart Product Crawling
                </h3>
                <p className="pr-[10px] text-base font-medium leading-relaxed text-body-color dark:text-muted-foreground">
                  Automatically extract products from Etsy, eBay, and Amazon with our Chrome extension
                </p>
              </div>
            </div>

            <div className="w-full">
              <div>
                <div className="mb-10 flex h-[70px] w-[70px] items-center justify-center rounded-md bg-primary/10 text-primary">
                  <Bot className="h-8 w-8" />
                </div>
                <h3 className="mb-5 text-xl font-bold text-black dark:text-white sm:text-2xl lg:text-xl xl:text-2xl">
                  Automated Listing
                </h3>
                <p className="pr-[10px] text-base font-medium leading-relaxed text-body-color dark:text-muted-foreground">
                  Create and upload products to TikTok Shop using customizable templates and schedules
                </p>
              </div>
            </div>

            <div className="w-full">
              <div>
                <div className="mb-10 flex h-[70px] w-[70px] items-center justify-center rounded-md bg-primary/10 text-primary">
                  <ShoppingCart className="h-8 w-8" />
                </div>
                <h3 className="mb-5 text-xl font-bold text-black dark:text-white sm:text-2xl lg:text-xl xl:text-2xl">
                  Order Management
                </h3>
                <p className="pr-[10px] text-base font-medium leading-relaxed text-body-color dark:text-muted-foreground">
                  Sync orders, auto-purchase shipping labels, and fulfill through Gearment integration
                </p>
              </div>
            </div>

            <div className="w-full">
              <div>
                <div className="mb-10 flex h-[70px] w-[70px] items-center justify-center rounded-md bg-primary/10 text-primary">
                  <Zap className="h-8 w-8" />
                </div>
                <h3 className="mb-5 text-xl font-bold text-black dark:text-white sm:text-2xl lg:text-xl xl:text-2xl">
                  Template System
                </h3>
                <p className="pr-[10px] text-base font-medium leading-relaxed text-body-color dark:text-muted-foreground">
                  Design reusable product templates with variants, pricing, and descriptions
                </p>
              </div>
            </div>

            <div className="w-full">
              <div>
                <div className="mb-10 flex h-[70px] w-[70px] items-center justify-center rounded-md bg-primary/10 text-primary">
                  <Globe className="h-8 w-8" />
                </div>
                <h3 className="mb-5 text-xl font-bold text-black dark:text-white sm:text-2xl lg:text-xl xl:text-2xl">
                  Multi-Shop Support
                </h3>
                <p className="pr-[10px] text-base font-medium leading-relaxed text-body-color dark:text-muted-foreground">
                  Manage multiple TikTok shops from a single dashboard with role-based access
                </p>
              </div>
            </div>

            <div className="w-full">
              <div>
                <div className="mb-10 flex h-[70px] w-[70px] items-center justify-center rounded-md bg-primary/10 text-primary">
                  <Shield className="h-8 w-8" />
                </div>
                <h3 className="mb-5 text-xl font-bold text-black dark:text-white sm:text-2xl lg:text-xl xl:text-2xl">
                  Secure Integration
                </h3>
                <p className="pr-[10px] text-base font-medium leading-relaxed text-body-color dark:text-muted-foreground">
                  Official TikTok Shop API integration with secure authentication and token management
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="relative z-10 py-16 md:py-20 lg:py-28 bg-gray-light dark:bg-bg-color-dark">
        <div className="container">
          <div className="mx-auto max-w-[570px] text-center mb-[100px]">
            <h2 className="mb-4 text-3xl font-bold !leading-tight text-black dark:text-white sm:text-4xl md:text-[45px]">
              How It Works
            </h2>
            <p className="text-base !leading-relaxed text-body-color dark:text-muted-foreground md:text-lg">
              Get started in minutes with our streamlined workflow
            </p>
          </div>

          <div className="grid grid-cols-1 gap-x-8 gap-y-14 md:grid-cols-3">
            <div className="w-full text-center">
              <div className="relative mx-auto mb-10 flex h-[70px] w-[70px] items-center justify-center rounded-md bg-primary/10 text-primary">
                <span className="text-2xl font-bold">1</span>
                <span className="absolute -right-3 -top-3 z-[-1] h-full w-full rounded-md bg-primary/20"></span>
              </div>
              <h3 className="mb-5 text-xl font-bold text-black dark:text-white sm:text-2xl lg:text-xl xl:text-2xl">
                Connect Your Shop
              </h3>
              <p className="text-base font-medium leading-relaxed text-body-color dark:text-muted-foreground">
                Securely connect your TikTok Shop using our official API integration.
                Multiple shops supported.
              </p>
            </div>

            <div className="w-full text-center">
              <div className="relative mx-auto mb-10 flex h-[70px] w-[70px] items-center justify-center rounded-md bg-primary/10 text-primary">
                <span className="text-2xl font-bold">2</span>
                <span className="absolute -right-3 -top-3 z-[-1] h-full w-full rounded-md bg-primary/20"></span>
              </div>
              <h3 className="mb-5 text-xl font-bold text-black dark:text-white sm:text-2xl lg:text-xl xl:text-2xl">
                Crawl & Create
              </h3>
              <p className="text-base font-medium leading-relaxed text-body-color dark:text-muted-foreground">
                Use our Chrome extension to find products on major marketplaces.
                Create templates and automate listings.
              </p>
            </div>

            <div className="w-full text-center">
              <div className="relative mx-auto mb-10 flex h-[70px] w-[70px] items-center justify-center rounded-md bg-primary/10 text-primary">
                <span className="text-2xl font-bold">3</span>
                <span className="absolute -right-3 -top-3 z-[-1] h-full w-full rounded-md bg-primary/20"></span>
              </div>
              <h3 className="mb-5 text-xl font-bold text-black dark:text-white sm:text-2xl lg:text-xl xl:text-2xl">
                Automate & Scale
              </h3>
              <p className="text-base font-medium leading-relaxed text-body-color dark:text-muted-foreground">
                Let our system handle orders, shipping, and fulfillment automatically.
                Focus on growing your business.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 md:py-20 lg:py-28">
        <div className="container">
          <div className="grid grid-cols-2 gap-8 md:grid-cols-4">
            <div className="text-center">
              <div className="mb-4 text-4xl font-bold text-primary sm:text-5xl lg:text-6xl xl:text-7xl">
                10K+
              </div>
              <div className="text-base font-medium text-body-color dark:text-muted-foreground">
                Products Crawled
              </div>
            </div>
            <div className="text-center">
              <div className="mb-4 text-4xl font-bold text-primary sm:text-5xl lg:text-6xl xl:text-7xl">
                500+
              </div>
              <div className="text-base font-medium text-body-color dark:text-muted-foreground">
                Active Shops
              </div>
            </div>
            <div className="text-center">
              <div className="mb-4 text-4xl font-bold text-primary sm:text-5xl lg:text-6xl xl:text-7xl">
                99.9%
              </div>
              <div className="text-base font-medium text-body-color dark:text-muted-foreground">
                Uptime
              </div>
            </div>
            <div className="text-center">
              <div className="mb-4 text-4xl font-bold text-primary sm:text-5xl lg:text-6xl xl:text-7xl">
                24/7
              </div>
              <div className="text-base font-medium text-body-color dark:text-muted-foreground">
                Automation
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="relative z-10 py-16 md:py-20 lg:py-28 bg-gray-light dark:bg-bg-color-dark">
        <div className="container">
          <div className="mx-auto max-w-[665px] text-center mb-[100px]">
            <h2 className="mb-4 text-3xl font-bold !leading-tight text-black dark:text-white sm:text-4xl md:text-[45px]">
              Simple, Transparent Pricing
            </h2>
            <p className="text-base !leading-relaxed text-body-color dark:text-muted-foreground md:text-lg">
              Start free, scale as you grow. No hidden fees or setup costs.
            </p>
          </div>

          {/* Desktop: 3-column layout, Mobile: Vertical stack */}
          <div className="grid grid-cols-1 gap-8 lg:grid-cols-3 lg:gap-6 xl:gap-8 mb-12">

            {/* Free Plan */}
            <div className="order-2 lg:order-1 rounded-sm bg-white p-8 shadow-two dark:bg-dark">
              <div className="text-center mb-8">
                <h3 className="mb-2 text-xl font-bold text-black dark:text-white">
                  Free
                </h3>
                <p className="text-sm text-body-color dark:text-muted-foreground mb-4">Start Selling POD on TikTok</p>
                <div className="mb-6">
                  <span className="text-4xl font-bold text-black dark:text-white">$0</span>
                  <span className="text-base font-medium text-body-color dark:text-muted-foreground">/forever</span>
                </div>
              </div>
              <div className="mb-8">
                <ul className="space-y-3">
                  <li className="flex items-center">
                    <CheckCircle className="mr-3 h-4 w-4 text-primary" />
                    <span className="text-sm font-medium text-body-color dark:text-muted-foreground">
                      Perfect for beginners
                    </span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-3 h-4 w-4 text-primary" />
                    <span className="text-sm font-medium text-body-color dark:text-muted-foreground">
                      Connect unlimited stores
                    </span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-3 h-4 w-4 text-primary" />
                    <span className="text-sm font-medium text-body-color dark:text-muted-foreground">
                      Try basic tools
                    </span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-3 h-4 w-4 text-primary" />
                    <span className="text-sm font-medium text-body-color dark:text-muted-foreground">
                      No credit card required
                    </span>
                  </li>
                </ul>
              </div>
              <Link
                href="/login"
                className="block w-full rounded-sm bg-gray-200 dark:bg-gray-700 p-3 text-center text-base font-semibold text-black dark:text-white transition duration-300 ease-in-out hover:bg-gray-300 dark:hover:bg-gray-600"
              >
                Get Started for Free
              </Link>
            </div>

            {/* Creator Plan - Highlighted */}
            <div className="order-1 lg:order-2 relative rounded-sm bg-white p-8 shadow-two dark:bg-dark border-2 border-primary lg:scale-105">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-primary px-4 py-1 text-sm font-semibold text-white rounded-sm">
                  ⭐️ Most Popular
                </span>
              </div>
              <div className="text-center mb-8">
                <h3 className="mb-2 text-xl font-bold text-black dark:text-white">
                  Creator
                </h3>
                <p className="text-sm text-primary font-medium mb-4">Best for active sellers</p>
                <div className="mb-6">
                  <span className="text-5xl font-bold text-primary">$5</span>
                  <span className="text-base font-medium text-body-color dark:text-muted-foreground">/store/month</span>
                </div>
              </div>
              <div className="mb-8">
                <ul className="space-y-3">
                  <li className="flex items-center">
                    <CheckCircle className="mr-3 h-4 w-4 text-primary" />
                    <span className="text-sm font-medium text-body-color dark:text-muted-foreground">
                      For sellers with real traffic & daily orders
                    </span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-3 h-4 w-4 text-primary" />
                    <span className="text-sm font-medium text-body-color dark:text-muted-foreground">
                      Upload up to 100 products/store/month
                    </span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-3 h-4 w-4 text-primary" />
                    <span className="text-sm font-medium text-body-color dark:text-muted-foreground">
                      Fulfill up to 300 orders/store/month
                    </span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-3 h-4 w-4 text-primary" />
                    <span className="text-sm font-medium text-body-color dark:text-muted-foreground">
                      Access to automation tools
                    </span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-3 h-4 w-4 text-primary" />
                    <span className="text-sm font-medium text-body-color dark:text-muted-foreground">
                      Bulk upload, auto-design, auto-fulfill
                    </span>
                  </li>
                </ul>
              </div>
              <div className="text-center mb-4">
                <p className="text-sm font-medium text-primary">🎯 Recommended for 95% of sellers</p>
              </div>
              <Link
                href="/login"
                className="block w-full rounded-sm bg-primary p-4 text-center text-base font-semibold text-white transition duration-300 ease-in-out hover:bg-primary/80"
              >
                Start Selling with Creator
              </Link>
            </div>

            {/* Growth Plan */}
            <div className="order-3 lg:order-3 rounded-sm bg-white p-8 shadow-two dark:bg-dark">
              <div className="text-center mb-8">
                <h3 className="mb-2 text-xl font-bold text-black dark:text-white flex items-center justify-center">
                  Growth <span className="ml-2">🚀</span>
                </h3>
                <p className="text-sm text-body-color dark:text-muted-foreground mb-4">Unlock unlimited scale (only when you need it)</p>
                <div className="mb-6">
                  <span className="text-4xl font-bold text-black dark:text-white">$7</span>
                  <span className="text-base font-medium text-body-color dark:text-muted-foreground">/store/month</span>
                </div>
              </div>
              <div className="mb-8">
                <ul className="space-y-3">
                  <li className="flex items-center">
                    <CheckCircle className="mr-3 h-4 w-4 text-primary" />
                    <span className="text-sm font-medium text-body-color dark:text-muted-foreground">
                      Everything in Creator, but no limits
                    </span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-3 h-4 w-4 text-primary" />
                    <span className="text-sm font-medium text-body-color dark:text-muted-foreground">
                      Ideal for stores handling hundreds of orders daily
                    </span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-3 h-4 w-4 text-primary" />
                    <span className="text-sm font-medium text-body-color dark:text-muted-foreground">
                      Scale effortlessly, no caps, no throttling
                    </span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-3 h-4 w-4 text-primary" />
                    <span className="text-sm font-medium text-body-color dark:text-muted-foreground">
                      Priority support
                    </span>
                  </li>
                </ul>
              </div>
              <div className="text-center mb-4">
                <p className="text-sm font-medium text-body-color dark:text-muted-foreground">💬 Switch to Growth anytime as your store grows</p>
              </div>
              <Link
                href="/login"
                className="block w-full rounded-sm bg-transparent border-2 border-primary p-3 text-center text-base font-semibold text-primary transition duration-300 ease-in-out hover:bg-primary hover:text-white"
              >
                Upgrade When Ready
              </Link>
            </div>
          </div>

          {/* Big Team - Full width banner */}
          <div className="rounded-sm bg-gray-800 dark:bg-gray-900 p-8 lg:p-12">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
              <div className="mb-6 lg:mb-0 lg:flex-1">
                <div className="flex items-center mb-4">
                  <h3 className="text-2xl font-bold text-white mr-4">Big Team</h3>
                  <span className="bg-yellow-500 px-3 py-1 text-xs font-semibold text-black rounded-sm">
                    Enterprise
                  </span>
                </div>
                <p className="text-gray-300 mb-4">Custom for high-volume partners</p>
                <div className="flex items-center mb-4">
                  <span className="text-3xl font-bold text-white mr-2">$4</span>
                  <span className="text-gray-300">/store/month (for 200+ stores)</span>
                </div>
                <ul className="space-y-2 text-sm text-gray-300">
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-yellow-500" />
                    Custom API, SLA, advanced analytics
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-yellow-500" />
                    Dedicated support & account manager
                  </li>
                </ul>
              </div>
              <div className="lg:ml-8">
                <Link
                  href="/contact"
                  className="inline-block rounded-sm bg-transparent border-2 border-white px-8 py-3 text-base font-semibold text-white transition duration-300 ease-in-out hover:bg-white hover:text-gray-800"
                >
                  Contact Sales
                </Link>
              </div>
            </div>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 z-[-1]">
          <svg
            width="239"
            height="601"
            viewBox="0 0 239 601"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <rect
              opacity="0.3"
              x="-184.451"
              y="600.973"
              width="196"
              height="541.607"
              rx="2"
              transform="rotate(-128.7 -184.451 600.973)"
              fill="url(#paint0_linear_93:235)"
            />
            <rect
              opacity="0.3"
              x="-188.201"
              y="385.272"
              width="59.7544"
              height="541.607"
              rx="2"
              transform="rotate(-128.7 -188.201 385.272)"
              fill="url(#paint1_linear_93:235)"
            />
            <defs>
              <linearGradient
                id="paint0_linear_93:235"
                x1="-90.1184"
                y1="420.414"
                x2="-90.1184"
                y2="1131.65"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#4A6CF7" />
                <stop offset="1" stopColor="#4A6CF7" stopOpacity="0" />
              </linearGradient>
              <linearGradient
                id="paint1_linear_93:235"
                x1="-159.441"
                y1="204.714"
                x2="-159.441"
                y2="915.952"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#4A6CF7" />
                <stop offset="1" stopColor="#4A6CF7" stopOpacity="0" />
              </linearGradient>
            </defs>
          </svg>
        </div>
      </section>

      {/* Testimonials Section */}
      <Testimonials />

      {/* CTA Section */}
      <section className="py-16 md:py-20 lg:py-28">
        <div className="container">
          <div className="mx-auto max-w-[570px] text-center">
            <h2 className="mb-4 text-3xl font-bold !leading-tight text-black dark:text-white sm:text-4xl md:text-[45px]">
              Ready to Scale Your TikTok Shop?
            </h2>
            <p className="mb-12 text-base !leading-relaxed text-body-color dark:text-muted-foreground md:text-lg">
              Join hundreds of successful sellers who trust our platform to automate
              and grow their TikTok Shop business.
            </p>
            <div className="flex flex-col items-center justify-center space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0">
              <Link
                href="/login"
                className="rounded-sm bg-primary px-8 py-4 text-base font-semibold text-white duration-300 ease-in-out hover:bg-primary/80 inline-flex items-center"
              >
                Get Started Free
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </div>
            <p className="mt-8 text-sm text-body-color dark:text-muted-foreground">
              No credit card required • 3-day free trial • Cancel anytime
            </p>
          </div>
        </div>
      </section>
      <Footer />
    </>
  );
}
